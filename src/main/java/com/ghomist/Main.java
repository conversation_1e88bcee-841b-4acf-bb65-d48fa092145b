package com.ghomist;

import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import co.elastic.clients.util.ObjectBuilder;
import java.util.Arrays;
import java.util.function.Function;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;

public class Main {
  public static void main(String[] args) {
    String str = "";
    String dateFrom = "";
    String dateTo = "";
    long[] longList = new long[] {1, 2, 3, 4, 5};

    var builder = new BoolQuery.Builder();

    // termQuery
    builder.filter(QueryBuilders.term().field("abc").value(str).build());
    builder.filter(f -> f.term(q -> q.field("abc").value(str)));

    // rangeQuery
    builder.filter(QueryBuilders.range().date(d -> d.gte(dateFrom).lte(dateTo)).build());
    builder.filter(f -> f.range(b -> b.date(d -> d.gte(dateFrom).lte(dateTo))));

    var list =
        Arrays.stream(longList)
            .mapToObj(l -> new FieldValue.Builder().longValue(l).build())
            .toList();

    // termsQuery
    builder.filter(QueryBuilders.terms(a -> a.field("abc").terms(x -> x.value(list))).terms());
    builder.filter(f -> f.terms(b -> b.field("abc").terms(t -> t.value(list))));

    // mustNot
    builder.mustNot(QueryBuilders.term("jobType", WorkflowJobServiceImpl.INTERNAL));

    var ret = new NativeQueryBuilder().withFilter(builder.build()._toQuery());
  }
}
